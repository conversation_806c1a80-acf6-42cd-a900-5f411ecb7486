../../Scripts/mcp-server-git.exe,sha256=-a5T1aLFJdxVyHGSxqqGJ4LJT9Qvx6-5ogHSbkpjOWQ,108401
mcp_server_git-2025.7.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mcp_server_git-2025.7.1.dist-info/METADATA,sha256=5B4jsnG9Po1zECc44diz9-Nqg28uwWwM4hr8IfL8HDE,10502
mcp_server_git-2025.7.1.dist-info/RECORD,,
mcp_server_git-2025.7.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mcp_server_git-2025.7.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
mcp_server_git-2025.7.1.dist-info/entry_points.txt,sha256=lNDfXZzZdY33F_H2B1kgXXeORLmy5YYmrFfbDAoy4DY,55
mcp_server_git-2025.7.1.dist-info/licenses/LICENSE,sha256=jMfG4zsk7U7o_MzDPszxAlSdBPpMuXN87Ml3Da0QgP8,1059
mcp_server_git/__init__.py,sha256=u1kAH2ucvcRXgB_DMrxPFIppcBWzC6x2XKRc-COuCSs,650
mcp_server_git/__main__.py,sha256=hsKGEXubWAgg4lB38E1tKJ3ZZp8FOFK8W1CT0-i8SR8,55
mcp_server_git/__pycache__/__init__.cpython-310.pyc,,
mcp_server_git/__pycache__/__main__.cpython-310.pyc,,
mcp_server_git/__pycache__/server.cpython-310.pyc,,
mcp_server_git/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mcp_server_git/server.py,sha256=vbEnlJNtYoKxQj04UCcscQr9_TJm3Gn_vXIvWBTa_xg,14070
